package com.erhgo.utils;

import com.google.common.annotations.VisibleForTesting;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Date;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateTimeUtils {

    public static String ZONE = "Europe/Paris";
    public static ZoneId ZONE_ID;

    static {
        refreshZoneId();
    }


    @VisibleForTesting
    public static void refreshZoneId() {
        ZONE_ID = ZoneId.getAvailableZoneIds().contains(ZONE) ? ZoneId.of(ZONE) : ZoneId.systemDefault();
    }

    public static OffsetDateTime millisToOffsetDateTime(Long timestamp) {
        return timestamp == null ? null : OffsetDateTime.ofInstant(Instant.ofEpochMilli(timestamp), DateTimeUtils.ZONE_ID);
    }

    public static OffsetDateTime instantToOffsetDateTime(Instant timestamp) {
        return timestamp == null ? null : OffsetDateTime.ofInstant((timestamp), DateTimeUtils.ZONE_ID);
    }

    public static ZoneOffset zoneOffset() {
        return ZONE_ID.getRules().getOffset(LocalDateTime.now());
    }

    public static LocalDate dateToLocalDate(Date date) {
        return date
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    public static Date startOfTodayMinusDaysToDate(int nbDays) {
        return Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).minusDays(nbDays).toInstant());
    }

    public static OffsetDateTime localDateTimeToOffsetDateTime(LocalDateTime localDateTime) {
        return localDateTime == null ? null : localDateTime.atOffset(zoneOffset());
    }

    public static Date localDateToDate(LocalDateTime date) {
        return new Date(date.toEpochSecond(zoneOffset()) * 1_000);
    }

    public static OffsetDateTime dateToOffsetDate(Date date) {
        return date == null ? null : OffsetDateTime.ofInstant(date.toInstant(), ZONE_ID);
    }

    public static OffsetDateTime timestampToOffsetDateTime(Long lastConnectionTimestampInMs) {
        return lastConnectionTimestampInMs == null ? null : OffsetDateTime.ofInstant(Instant.ofEpochMilli(lastConnectionTimestampInMs), ZONE_ID);
    }

    public static Date offsetDateToDate(OffsetDateTime date) {
        return date == null ? null : new Date(date.toInstant().getEpochSecond() * 1000);
    }

    public static String convertDateToString(Date date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
        return date.toInstant()
                .atZone(ZONE_ID)
                .toLocalDateTime()
                .format(formatter);
    }

    public static String formatDate(TemporalAccessor date) {
        return date == null ? "" : DateTimeFormatter.ofPattern("dd/MM/yyyy").format(date);
    }

    public static LocalDateTime offsetDateTimeToLocalDateTime(OffsetDateTime updatedAt) {
        return updatedAt == null ? null : updatedAt.toLocalDateTime();
    }

    public static OffsetDateTime parseDate(String dateStr) {
        if (StringUtils.trimToNull(dateStr) == null) {
            return null;
        }
        var longPattern = "dd/MM/yyyy HH:mm";
        var shortPattern = "dd/MM/yyyy";
        var trimmed = dateStr.trim();

        if (trimmed.length() >= longPattern.length()) {
            var trimmedDate = trimmed.substring(0, longPattern.length());
            try {
                var dateTimeFormatter = DateTimeFormatter.ofPattern(longPattern);
                var localDateTime = LocalDateTime.parse(trimmedDate, dateTimeFormatter);
                return localDateTime.atZone(ZONE_ID).toOffsetDateTime();
            } catch (RuntimeException e) {
                log.debug("Unable to parse date {} with format dd/MM/yyyy HH:mm", trimmedDate, e);
            }
        }

        if (trimmed.length() >= shortPattern.length()) {
            var trimmedDate = trimmed.substring(0, shortPattern.length());
            try {
                var dateFormatter = DateTimeFormatter.ofPattern(shortPattern);
                var localDate = LocalDate.parse(trimmedDate, dateFormatter);
                LocalDateTime localDateTime = localDate.atStartOfDay();
                return localDateTime.atZone(ZONE_ID).toOffsetDateTime();
            } catch (RuntimeException e) {
                log.debug("Unable to parse date {} with format dd/MM/yyyy", trimmedDate, e);
            }
        }

        log.warn("Unable to parse date {} - string too short or invalid format", trimmed);
        return null;
    }

    public static Date instantToDate(Instant instant) {
        return new Date(instant.toEpochMilli());
    }
}
