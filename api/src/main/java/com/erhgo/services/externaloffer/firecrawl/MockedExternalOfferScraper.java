package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.openapi.dto.ScrapeOffersCommandDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@ConditionalOnExpression("T(org.springframework.util.StringUtils).isEmpty('${firecrawl.api-key:}')")
@Slf4j
public abstract class MockedExternalOfferScraper implements ExternalOfferScraper {

    private final ObjectMapper objectMapper;

    @Override
    public List<String> scrapeJobsAtUrl(String url, String mustContain, String mustNotContain) throws InvalidScrapingException {
        log.info("Mock scraping job URLs from {}", url);
        throw new IllegalStateException("Mock scraper not implemented");
    }

    @Override
    public JsonNode scrapeJobDetails(String jobUrl) {
        log.info("Mock scraping job details from {}", jobUrl);
        return objectMapper.createObjectNode();
    }

    @Override
    public void scrapeOffers(ScrapeOffersCommandDTO command) {
        log.info("Mock scraping offers with command: {}", command);
    }
}
