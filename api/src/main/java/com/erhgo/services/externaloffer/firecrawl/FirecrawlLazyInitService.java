package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.services.externaloffer.OfferLazyInitializer;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service("firecrawlLazyInitService")
@RequiredArgsConstructor
public class FirecrawlLazyInitService implements OfferLazyInitializer<ScrappedJob> {

    private final ObjectMapper objectMapper;
    private final ExternalOfferScraper externalOfferScraper;

    @PostConstruct
    public void init() {
        log.info("FirecrawlLazyInitService initialized");
    }

    @Override
    public ScrappedJob init(ScrappedJob job, AtsGetOfferConfig config) {
        if (StringUtils.isNotBlank(job.getJobDescription())) {
            return job;
        }
        try {
            log.debug("Fetching job details for URL: {}", job.getId());
            var jobDetails = externalOfferScraper.scrapeJobDetails(job.getId());

            if (jobDetails == null || jobDetails.isEmpty()) {
                log.warn("No job details found for URL: {}", job.getId());
                return job;
            }

            var scrapedDetails = objectMapper.treeToValue(jobDetails, ScrappedJob.class);

            job.setJobDescription(scrapedDetails.getJobDescription());
            job.setOrganizationDescription(scrapedDetails.getOrganizationDescription());
            job.setOfferTitle(scrapedDetails.getOfferTitle());
            job.setContractType(scrapedDetails.getContractType());
            job.setLocation(scrapedDetails.getLocation());
            job.setSalary(scrapedDetails.getSalary());
            job.setPublicationDate(LocalDateTime.now());
            job.setRawContent(objectMapper.writeValueAsString(jobDetails));

            log.debug("Firecrawl job {} initialized with details", job.getId());

        } catch (JsonProcessingException e) {
            log.error("Error fetching job details for URL: {}", job.getId(), e);
        }

        return job;
    }

}
