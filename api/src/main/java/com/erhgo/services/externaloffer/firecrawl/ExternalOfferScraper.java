package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.openapi.dto.ScrapeOffersCommandDTO;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;

public interface ExternalOfferScraper {
    List<String> scrapeJobsAtUrl(String url, String mustContain, String mustNotContain) throws InvalidScrapingException;

    JsonNode scrapeJobDetails(String jobUrl) throws InvalidScrapingException;

    void scrapeOffers(ScrapeOffersCommandDTO command);
}

