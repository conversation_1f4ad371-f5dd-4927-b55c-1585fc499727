package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.AbstractATSSynchronizer;
import com.erhgo.services.externaloffer.OfferDataExtractor;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class FirecrawlSynchronizer extends AbstractATSSynchronizer<ScrappedJob> {

    private final ExternalOfferScraper scraperService;

    @Getter
    private final GenericJobJsonParser<ScrappedJob> parser;


    public FirecrawlSynchronizer(
            ConfigurablePropertyRepository configurablePropertyRepository,
            ExternalOfferRepository repository,
            ExternalOfferRecruiterProvider externalOfferRecruiterProvider,
            AtsGetOfferConfig atsConfig,
            ApplicationContext applicationContext,
            GenericJobJsonParser<ScrappedJob> parser,
            ExternalOfferScraper scraperService) {
        super(configurablePropertyRepository, repository, externalOfferRecruiterProvider, atsConfig, applicationContext);
        this.scraperService = scraperService;
        this.parser = parser;
    }


    @Override
    protected List<ExternalOffer> modifyOffers(Map<String, ScrappedJob> current, Map<String, ExternalOffer> previous) {
        log.debug("Ignored modification for Firecrawl");
        return new ArrayList<>();
    }


    @Override
    protected Page<ScrappedJob> fetchOffers(Map<String, List<String>> queryParams) {
        var url = getAtsConfig().getRemoteUrl();
        if (url == null || url.isEmpty()) {
            log.warn("No URL configured for FirecrawlSynchronizer");
            return new PageImpl<>(List.of());
        }

        if (getAtsConfig().getMaxPagesToScrape() != null && getAtsConfig().getMaxPagesToScrape() > 0) {
            return fetchJobsFromUrlPaginated(url);
        } else {
            return fetchJobsFromUrl(url);
        }

    }

    private Page<ScrappedJob> fetchJobsFromUrlPaginated(String url) {
        var maxPages = getAtsConfig().getMaxPagesToScrape();
        log.info("Starting paginated scraping with max {} pages using template: {}", maxPages, url);

        var allJobs = new ArrayList<ScrappedJob>();

        for (int page = 1; page <= maxPages; page++) {
            var paginatedUrl = url.replace("{page}", String.valueOf(page));
            log.info("Fetching job URLs from paginated URL: {} (page {}/{})", paginatedUrl, page, maxPages);

            var pageJobs = fetchJobsFromSingleUrl(paginatedUrl);
            if (pageJobs.isEmpty()) {
                log.info("No job URLs found at page {}, stopping pagination", page);
                break;
            }

            log.info("Found {} job URLs at page {}", pageJobs.size(), page);
            allJobs.addAll(pageJobs);
        }

        log.info("Total jobs collected: {}", allJobs.size());
        return new PageImpl<>(allJobs);
    }

    private Page<ScrappedJob> fetchJobsFromUrl(String url) {
        log.info("Fetching job URLs from URL: {}", url);
        var jobs = fetchJobsFromSingleUrl(url);

        if (jobs.isEmpty()) {
            log.warn("No job URLs found at {}", url);
            return new PageImpl<>(List.of());
        }

        log.info("Created {} minimal job references from {}", jobs.size(), url);
        return new PageImpl<>(jobs);
    }

    private List<ScrappedJob> fetchJobsFromSingleUrl(String url) {
        try {
            var jobUrls = scraperService.scrapeJobsAtUrl(url,
                    getAtsConfig().getOfferUrlMustContain(),
                    getAtsConfig().getOfferUrlMustNotContain());

            return jobUrls.stream()
                    .map(jobUrl -> new ScrappedJob().setId(jobUrl))
                    .toList();

        } catch (RuntimeException e) {
            log.error("Error fetching job URLs from {}: {}", url, e.getMessage());
            throw new GenericTechnicalException("Failed to fetch job URLs", e);
        }
    }

    @Override
    protected String fetchRemoteOffersRawContent(Map queryParams) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    public void simulateAtsOffer(AtsOfferSimulatedDTO offer) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    protected OfferDataExtractor<ScrappedJob> getOfferDataExtractor() {
        throw new UnsupportedOperationException("Not implemented");
    }
}
