package com.erhgo.domain.userprofile;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import jakarta.persistence.*;
import lombok.*;

@Embeddable
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@Builder
public class UserRegistrationState {
    @AllArgsConstructor
    public enum RegistrationStep {
        CREATED_ACCOUNT("1/6 - Compte créé"),
        CONFIRMED_SITUATION("2/6 - Situation confirmée"),
        CONFIRMED_PROFESSION_DIRECT("3/6 - Métier confirmé directement"),
        CONFIRMED_CITY("4/6 - Ville confirmée"),
        CONFIRMED_SALARY("5/6 - Salaire confirmé"),
        CONFIRMED_CONTACT_DETAILS("6/6 - Inscription finalisée"),
        LEGACY_ACCOUNT("Ancien compte"),
        NOT_AFFECTED("Compte créé dans le cadre d'une candidature"),
        BO_INITIALIZED("Compte initialisé depuis le BO mais non confirmé"),
        BO_CONFIRMED("Compte initialisé et confirmée depuis le BO"),
        ;

        @Getter
        private String label;
    }

    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false)
    @Getter
    @Builder.Default
    private RegistrationStep registrationStep = RegistrationStep.CREATED_ACCOUNT;

    @Getter
    @Setter
    @ManyToOne
    private ErhgoOccupation selectedOccupation;

    @Getter
    @Setter
    @Column(length = 2000)
    private String jobTitle;

    public void updateRegistrationStep(RegistrationStep registrationStep) {
        this.registrationStep = registrationStep;
    }
}
