package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import com.erhgo.services.externaloffer.recruiterdispatcher.fetch.ExternalOfferRecruiterProvider;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;


import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FirecrawlPaginationTest {

    private static final String ATS_CODE = "FIRECRAWL";
    private static final String CONFIG_CODE = "EST_METROPOLE";
    private static final String RECRUITER_CODE = "S-21912";
    private static final String TEST_URL = "https://example.com/jobs?page={page}";
    private static final String JOB_INVITE_FILTER = "job-invite";
    private final ObjectMapper realObjectMapper = createObjectMapper();
    @InjectMocks
    private FirecrawlScraperService scraperService;
    @Mock
    private RestTemplate restTemplate;


    @Mock
    private ConfigurablePropertyRepository configurablePropertyRepository;

    @Mock
    private ExternalOfferRepository repository;

    @Mock
    private ExternalOfferRecruiterProvider externalOfferRecruiterProvider;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private GenericJobJsonParser<ScrappedJob> parser;

    private FirecrawlSynchronizer synchronizer;


    private static ObjectMapper createObjectMapper() {
        return new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule());
    }

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scraperService, "restTemplate", restTemplate);
        ReflectionTestUtils.setField(scraperService, "objectMapper", realObjectMapper);
        ReflectionTestUtils.setField(scraperService, "apiUrl", "https://api.example.com");
        ReflectionTestUtils.setField(scraperService, "apiKey", "api-key");

    }

    @SneakyThrows
    @Test
    void fetchOffers_shouldHandlePaginationCorrectly() {
        var atsConfig = new AtsGetOfferConfig()
                .setAtsCode(ATS_CODE)
                .setConfigCode(CONFIG_CODE)
                .setRecruiterCode(RECRUITER_CODE)
                .setRemoteUrl(TEST_URL)
                .setMaxPagesToScrape(3)
                .setOfferUrlMustContain(JOB_INVITE_FILTER);

        synchronizer = new FirecrawlSynchronizer(
                configurablePropertyRepository,
                repository,
                externalOfferRecruiterProvider,
                atsConfig,
                applicationContext,
                parser,
                scraperService
        );

        var page1Response = """
                {
                  "data": {
                    "links": [
                      "https://example.com/job-invite/1",
                      "https://example.com/job-invite/2"
                    ]
                  }
                }
                """;

        var page2Response = """
                {
                  "data": {
                    "links": [
                      "https://example.com/job-invite/3"
                    ]
                  }
                }
                """;

        var page3Response = """
                {
                  "data": {
                    "links": []
                  }
                }
                """;

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(
                new ResponseEntity<>(page1Response, HttpStatus.OK),
                new ResponseEntity<>(page2Response, HttpStatus.OK),
                new ResponseEntity<>(page3Response, HttpStatus.OK)
        );

        var result = synchronizer.fetchOffers(Map.of());

        assertThat(result.getContent()).hasSize(3);
        assertThat(result.getContent().get(0).getId()).isEqualTo("https://example.com/job-invite/1");
        assertThat(result.getContent().get(1).getId()).isEqualTo("https://example.com/job-invite/2");
        assertThat(result.getContent().get(2).getId()).isEqualTo("https://example.com/job-invite/3");

        verify(restTemplate, times(3)).exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        );
    }
}
