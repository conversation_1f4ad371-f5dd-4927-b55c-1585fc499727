package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import jakarta.persistence.EntityManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class FirecrawlGlobalLimiterIntegrationTest extends AbstractIntegrationTest {

    private static final String ATS_CODE = "FIRECRAWL";
    private static final String CONFIG_CODE = "STEF";
    private static final String RECRUITER_CODE = "S-21912";
    private static final String TEST_URL = "https://stef.jobs/fr/postuler/";
    private static final String LIMITER_SERVICE_NAME = "globalLimiter";
    private static final String OFFER_URL_MUST_CONTAIN = "job-invite";
    private static final String OFFER_URL_MUST_NOT_CONTAIN = "candidature-spontanee";
    private static final int LIMITER_VALUE = 8;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ExternalOfferRepository externalOfferRepository;

    @Autowired
    private ExternalOfferServiceProvider serviceProvider;

    @MockBean
    private FirecrawlScraperService scraperService;

    @Autowired
    private GenericJobJsonParser<ScrappedJob> firecrawlJobParser;

    @MockBean
    private ExternalOfferRecruitmentService externalOfferRecruitmentService;

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void ensureGlobalLimiterAppliesCorrectLimits() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                            ('ats.limit.global.FIRECRAWL--STEF', '%s')
                    ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                    """.formatted(LIMITER_VALUE);
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });

        applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(RECRUITER_CODE)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        var config = new AtsGetOfferConfig()
                .setAtsCode(ATS_CODE)
                .setConfigCode(CONFIG_CODE)
                .setRecruiterCode(RECRUITER_CODE)
                .setLimiterServiceName(LIMITER_SERVICE_NAME)
                .setOfferUrlMustContain(OFFER_URL_MUST_CONTAIN)
                .setOfferUrlMustNotContain(OFFER_URL_MUST_NOT_CONTAIN)
                .setRemoteUrl(TEST_URL);

        List<String> jobUrls = List.of(
                "https://example.com/careers/job-invite/1",
                "https://example.com/careers/job-invite/2",
                "https://example.com/careers/job-invite/3",
                "https://example.com/careers/job-invite/4",
                "https://example.com/careers/job-invite/5",
                "https://example.com/careers/job-invite/6",
                "https://example.com/careers/job-invite/7",
                "https://example.com/careers/job-invite/8",
                "https://example.com/careers/job-invite/9",
                "https://example.com/careers/job-invite/10"
        );

        when(scraperService.scrapeJobsAtUrl(anyString(), anyString(), anyString())).thenReturn(jobUrls);

        serviceProvider.initializeCustomService(FirecrawlSynchronizer.class, config, firecrawlJobParser);
        serviceProvider.getService(config).fetchAndUpdateOffers();

        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig(ATS_CODE, RECRUITER_CODE, CONFIG_CODE);
            Assertions.assertThat(offers).hasSize(LIMITER_VALUE);
        });
    }
}
