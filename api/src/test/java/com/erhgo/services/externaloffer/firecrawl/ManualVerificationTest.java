package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Manual verification test for per-client disable functionality.
 * This test can be run independently to verify the logic without requiring
 * the full Spring context or Java 21.
 */
class ManualVerificationTest {

    @Test
    void testAtsGetOfferConfigEnabledLogic() {
        // Test default behavior (enabled = null)
        var defaultConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("DEFAULT");
        
        assertThat(defaultConfig.isEnabled()).isTrue();
        assertThat(defaultConfig.getEnabled()).isTrue();

        // Test explicitly enabled
        var enabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("ENABLED")
                .setEnabled(true);
        
        assertThat(enabledConfig.isEnabled()).isTrue();
        assertThat(enabledConfig.getEnabled()).isTrue();

        // Test explicitly disabled
        var disabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("DISABLED")
                .setEnabled(false);
        
        assertThat(disabledConfig.isEnabled()).isFalse();
        assertThat(disabledConfig.getEnabled()).isFalse();
    }

    @Test
    void testFilteringLogic() {
        // Simulate the filtering logic from FirecrawlConfiguration
        var configurations = List.of(
                new AtsGetOfferConfig()
                        .setAtsCode("FIRECRAWL")
                        .setConfigCode("STEF")
                        .setEnabled(true),
                new AtsGetOfferConfig()
                        .setAtsCode("FIRECRAWL")
                        .setConfigCode("EST_METROPOLE")
                        .setEnabled(false),
                new AtsGetOfferConfig()
                        .setAtsCode("FIRECRAWL")
                        .setConfigCode("APICIL")
                // enabled is null, should default to true
        );

        // Filter enabled configurations (simulating FirecrawlConfiguration logic)
        var enabledConfigurations = configurations.stream()
                .filter(AtsGetOfferConfig::isEnabled)
                .toList();

        // Verify results
        assertThat(enabledConfigurations).hasSize(2);
        assertThat(enabledConfigurations)
                .extracting(AtsGetOfferConfig::getConfigCode)
                .containsExactlyInAnyOrder("STEF", "APICIL");

        // Verify disabled configuration is filtered out
        assertThat(enabledConfigurations)
                .extracting(AtsGetOfferConfig::getConfigCode)
                .doesNotContain("EST_METROPOLE");
    }

    @Test
    void testEnvironmentVariableSimulation() {
        // Simulate different environment variable scenarios
        
        // Scenario 1: All enabled (default)
        var allEnabledConfigs = List.of(
                createConfig("STEF", null),
                createConfig("EST_METROPOLE", null),
                createConfig("APICIL", null)
        );
        
        var enabledCount1 = allEnabledConfigs.stream()
                .mapToInt(config -> config.isEnabled() ? 1 : 0)
                .sum();
        assertThat(enabledCount1).isEqualTo(3);

        // Scenario 2: EST_METROPOLE disabled
        var estDisabledConfigs = List.of(
                createConfig("STEF", true),
                createConfig("EST_METROPOLE", false),
                createConfig("APICIL", true)
        );
        
        var enabledConfigs2 = estDisabledConfigs.stream()
                .filter(AtsGetOfferConfig::isEnabled)
                .toList();
        assertThat(enabledConfigs2).hasSize(2);
        assertThat(enabledConfigs2)
                .extracting(AtsGetOfferConfig::getConfigCode)
                .containsExactlyInAnyOrder("STEF", "APICIL");

        // Scenario 3: Only APICIL enabled
        var onlyApicilConfigs = List.of(
                createConfig("STEF", false),
                createConfig("EST_METROPOLE", false),
                createConfig("APICIL", true)
        );
        
        var enabledConfigs3 = onlyApicilConfigs.stream()
                .filter(AtsGetOfferConfig::isEnabled)
                .toList();
        assertThat(enabledConfigs3).hasSize(1);
        assertThat(enabledConfigs3.get(0).getConfigCode()).isEqualTo("APICIL");
    }

    private AtsGetOfferConfig createConfig(String configCode, Boolean enabled) {
        var config = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode(configCode);
        
        if (enabled != null) {
            config.setEnabled(enabled);
        }
        
        return config;
    }

    @Test
    void testBackwardCompatibility() {
        // Test that existing configurations without the enabled property work correctly
        var legacyConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("LEGACY")
                .setRecruiterCode("S-12345")
                .setRemoteUrl("https://example.com");
        
        // Should default to enabled
        assertThat(legacyConfig.isEnabled()).isTrue();
        
        // Should be included in filtering
        var configs = List.of(legacyConfig);
        var filtered = configs.stream()
                .filter(AtsGetOfferConfig::isEnabled)
                .toList();
        
        assertThat(filtered).hasSize(1);
        assertThat(filtered.get(0).getConfigCode()).isEqualTo("LEGACY");
    }
}
