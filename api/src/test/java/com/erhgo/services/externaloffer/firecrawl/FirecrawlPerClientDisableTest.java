package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FirecrawlPerClientDisableTest {

    @Mock
    private ExternalOfferServiceProvider serviceProvider;

    @Mock
    private ParserProvider parserProvider;

    @Mock
    private ApplicationContext applicationContext;

    private FirecrawlConfiguration firecrawlConfiguration;

    @BeforeEach
    void setUp() {
        firecrawlConfiguration = new FirecrawlConfiguration(serviceProvider, parserProvider);
    }

    @Test
    void initializeServices_shouldSkipDisabledConfigurations() throws Exception {
        var enabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("STEF")
                .setEnabled(true);

        var disabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("EST_METROPOLE")
                .setEnabled(false);

        var defaultEnabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("APICIL");

        var configurations = List.of(enabledConfig, disabledConfig, defaultEnabledConfig);

        var spyConfiguration = spy(firecrawlConfiguration);
        doReturn(configurations).when(spyConfiguration).firecrawlConfigurations();

        when(parserProvider.initializeBean(any(), any())).thenReturn(mock(Object.class));

        spyConfiguration.initializeServices();

        verify(serviceProvider, times(2)).initializeCustomService(
                eq(FirecrawlSynchronizer.class),
                any(AtsGetOfferConfig.class),
                any()
        );

        // Verify that the enabled config was processed
        verify(serviceProvider).initializeCustomService(
                eq(FirecrawlSynchronizer.class),
                eq(enabledConfig),
                any()
        );

        // Verify that the default enabled config was processed
        verify(serviceProvider).initializeCustomService(
                eq(FirecrawlSynchronizer.class),
                eq(defaultEnabledConfig),
                any()
        );

        // Verify that the disabled config was NOT processed
        verify(serviceProvider, never()).initializeCustomService(
                eq(FirecrawlSynchronizer.class),
                eq(disabledConfig),
                any()
        );
    }

    @Test
    void atsGetOfferConfig_enabledProperty_shouldDefaultToTrue() {
        // Given: Config without explicit enabled property
        var config = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("TEST");

        // Then: Should default to enabled
        assertThat(config.isEnabled()).isTrue();
        assertThat(config.getEnabled()).isTrue();
    }

    @Test
    void atsGetOfferConfig_enabledProperty_shouldRespectExplicitValues() {
        // Given: Explicitly enabled config
        var enabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("ENABLED")
                .setEnabled(true);

        // Given: Explicitly disabled config
        var disabledConfig = new AtsGetOfferConfig()
                .setAtsCode("FIRECRAWL")
                .setConfigCode("DISABLED")
                .setEnabled(false);

        // Then: Should respect explicit values
        assertThat(enabledConfig.isEnabled()).isTrue();
        assertThat(enabledConfig.getEnabled()).isTrue();

        assertThat(disabledConfig.isEnabled()).isFalse();
        assertThat(disabledConfig.getEnabled()).isFalse();
    }
}
