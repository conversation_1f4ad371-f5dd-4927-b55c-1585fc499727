# Firecrawl Per-Client Disable Implementation

## Overview

This implementation adds the ability to disable web scraping on a per-client basis while maintaining the existing global disable functionality for Firecrawl ATS integrations.

## Changes Made

### 1. AtsGetOfferConfig.java

Added a new `enabled` property to allow per-client configuration:

```java
/**
 * Whether this specific ATS configuration is enabled (default: true)
 * This allows per-client disable functionality while maintaining global disable
 */
private Boolean enabled;

public Boolean getEnabled() {
    return BooleanUtils.isNotFalse(enabled); // Default to true if not explicitly set to false
}

public boolean isEnabled() {
    return getEnabled();
}
```

**Key Features:**
- Defaults to `true` when not explicitly set (backward compatibility)
- Uses `BooleanUtils.isNotFalse()` to handle null values gracefully
- Only returns `false` when explicitly set to `false`

### 2. application.yml

Updated the Firecrawl configuration to include per-client enabled properties:

```yaml
firecrawl:
  enabled: ${FIRECRAWL_ENABLED:true}  # Global disable (renamed from STEF_ENABLED)
  fetch:
    - atsCode: FIRECRAWL
      configCode: STEF
      # ... other config ...
      enabled: ${STEF_ENABLED:true}     # Per-client disable for STEF
      
    - atsCode: FIRECRAWL
      configCode: EST_METROPOLE
      # ... other config ...
      enabled: ${EST_METROPOLE_ENABLED:true}  # Per-client disable for EST_METROPOLE
      
    - atsCode: FIRECRAWL
      configCode: APICIL
      # ... other config ...
      enabled: ${APICIL_ENABLED:true}   # Per-client disable for APICIL
```

**Environment Variables:**
- `FIRECRAWL_ENABLED`: Global disable (controls entire Firecrawl module)
- `STEF_ENABLED`: Per-client disable for STEF
- `EST_METROPOLE_ENABLED`: Per-client disable for EST_METROPOLE
- `APICIL_ENABLED`: Per-client disable for APICIL

### 3. FirecrawlConfiguration.java

Modified the service initialization to filter out disabled configurations:

```java
@EventListener(ApplicationReadyEvent.class)
public void initializeServices() throws BeansException {
    firecrawlConfigurations().stream()
            .filter(config -> {
                if (!config.isEnabled()) {
                    log.info("Skipping disabled Firecrawl configuration: {}", config.getConfigCode());
                    return false;
                }
                return true;
            })
            .forEach(config -> {
                log.info("Initializing enabled Firecrawl configuration: {}", config.getConfigCode());
                var firecrawlParser = parserProvider.initializeBean(config.getAtsCode(), "firecrawlJobParser");
                serviceProvider.initializeCustomService(FirecrawlSynchronizer.class, config, firecrawlParser);
            });
}
```

**Key Features:**
- Filters configurations before service initialization
- Logs which configurations are skipped vs. initialized
- Only enabled configurations are registered with `ExternalOfferServiceProvider`

## Usage Examples

### Disable All Firecrawl Integrations (Global)
```bash
export FIRECRAWL_ENABLED=false
```
This disables the entire Firecrawl module - no services will be initialized.

### Disable Only EST_METROPOLE
```bash
export EST_METROPOLE_ENABLED=false
```
This disables only the EST_METROPOLE client while keeping STEF and APICIL active.

### Disable Multiple Clients
```bash
export STEF_ENABLED=false
export APICIL_ENABLED=false
```
This disables STEF and APICIL while keeping EST_METROPOLE active.

### Enable All (Default Behavior)
No environment variables needed - all clients are enabled by default.

## Backward Compatibility

- **Global disable**: Still works via `@ConditionalOnProperty(name = "ats.firecrawl.enabled", havingValue = "true")`
- **Default behavior**: All clients are enabled by default if no environment variables are set
- **Existing configurations**: Continue to work without modification

## Testing

The implementation includes unit tests in `FirecrawlPerClientDisableTest.java` that verify:

1. **Disabled configurations are skipped**: Configurations with `enabled: false` are not initialized
2. **Enabled configurations are processed**: Configurations with `enabled: true` are initialized
3. **Default behavior**: Configurations without explicit `enabled` property default to enabled
4. **Service registration**: Only enabled configurations are registered with the service provider

## Architecture Benefits

1. **Granular Control**: Can disable individual clients without affecting others
2. **Global Override**: Global disable still takes precedence over per-client settings
3. **Runtime Configuration**: Can be controlled via environment variables without code changes
4. **Logging**: Clear visibility into which configurations are enabled/disabled
5. **Backward Compatibility**: Existing deployments continue to work unchanged

## Implementation Notes

- The filtering happens at service initialization time, not at runtime
- Disabled services are never registered with `ExternalOfferServiceProvider`
- The `ExternalOfferScheduler` will only process registered (enabled) services
- Configuration changes require application restart to take effect
